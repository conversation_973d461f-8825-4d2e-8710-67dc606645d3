import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/models.dart' as models;
import '../services/firestore.dart';
import 'gemini_chat.dart';

class PersonaSelectionPage extends StatefulWidget {
  const PersonaSelectionPage({super.key});

  @override
  State<PersonaSelectionPage> createState() => _PersonaSelectionPageState();
}

class _PersonaSelectionPageState extends State<PersonaSelectionPage> {
  List<models.SystemPersona> _systemPersonas = [];
  List<models.SystemPersona> _orderedPersonas = [];
  String? _selectedPersonaId;
  bool _isLoading = true;
  String? _errorMessage;
  models.User? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Load user data and system personas in parallel
      final results = await Future.wait([
        FirestoreService.getUser(currentUser.uid),
        FirestoreService.getActiveSystemPersonas(),
      ]);

      _currentUser = results[0] as models.User?;
      _systemPersonas = results[1] as List<models.SystemPersona>;

      // Order personas: preferred first, then remaining
      _orderPersonas();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load personas: $e';
        _isLoading = false;
      });
    }
  }

  void _orderPersonas() {
    final preferredIds = _currentUser?.preferredPersonaIds ?? [];
    final preferredPersonas = <models.SystemPersona>[];
    final remainingPersonas = <models.SystemPersona>[];

    for (final persona in _systemPersonas) {
      if (persona.id != null && preferredIds.contains(persona.id)) {
        preferredPersonas.add(persona);
      } else {
        remainingPersonas.add(persona);
      }
    }

    // Sort preferred personas by their order in preferredPersonaIds
    preferredPersonas.sort((a, b) {
      final aIndex = preferredIds.indexOf(a.id!);
      final bIndex = preferredIds.indexOf(b.id!);
      return aIndex.compareTo(bIndex);
    });

    _orderedPersonas = [...preferredPersonas, ...remainingPersonas];
  }

  void _selectPersona(String? personaId) {
    setState(() {
      _selectedPersonaId = personaId;
    });
  }

  Future<void> _startChat() async {
    if (_selectedPersonaId == null) return;

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Create new chat with selected persona
      final chatId = await FirestoreService.createChat(
        currentUser.uid,
        systemPersonaId: _selectedPersonaId,
      );

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      // Navigate to GeminiChat with selected persona
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) =>
                GeminiChat(chatId: chatId, systemPersonaId: _selectedPersonaId),
          ),
        );
      }
    } catch (e) {
      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to start chat: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Coach'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: _buildBody(),
      floatingActionButton: _selectedPersonaId != null
          ? FloatingActionButton.extended(
              onPressed: _startChat,
              label: const Text('Start'),
              icon: const Icon(Icons.chat),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(onPressed: _loadData, child: const Text('Retry')),
          ],
        ),
      );
    }

    if (_orderedPersonas.isEmpty) {
      return const Center(child: Text('No coaches available'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header text
          Text(
            'Select a coach to start your conversation',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose the coaching style that best fits your current needs',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 24),

          // Preferred personas section
          if (_currentUser?.preferredPersonaIds.isNotEmpty == true) ...[
            Text(
              'Your Preferred Coaches',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            ..._getPreferredPersonas().map(
              (persona) => _buildPersonaCard(persona),
            ),
            const SizedBox(height: 24),

            if (_getRemainingPersonas().isNotEmpty) ...[
              Text(
                'Other Coaches',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),
            ],
          ],

          // All personas or remaining personas
          ...(_currentUser?.preferredPersonaIds.isEmpty == true
                  ? _orderedPersonas
                  : _getRemainingPersonas())
              .map((persona) => _buildPersonaCard(persona)),
        ],
      ),
    );
  }

  List<models.SystemPersona> _getPreferredPersonas() {
    final preferredIds = _currentUser?.preferredPersonaIds ?? [];
    return _orderedPersonas
        .where(
          (persona) => persona.id != null && preferredIds.contains(persona.id),
        )
        .toList();
  }

  List<models.SystemPersona> _getRemainingPersonas() {
    final preferredIds = _currentUser?.preferredPersonaIds ?? [];
    return _orderedPersonas
        .where(
          (persona) => persona.id == null || !preferredIds.contains(persona.id),
        )
        .toList();
  }

  Widget _buildPersonaCard(models.SystemPersona persona) {
    final isSelected = _selectedPersonaId == persona.id;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _selectPersona(persona.id),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : null,
          ),
          child: Row(
            children: [
              // Avatar
              CircleAvatar(
                radius: 30,
                backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                backgroundImage:
                    persona.avatarUrl != null && persona.avatarUrl!.isNotEmpty
                    ? (persona.avatarUrl!.startsWith('assets/')
                          ? AssetImage(persona.avatarUrl!) as ImageProvider
                          : NetworkImage(persona.avatarUrl!))
                    : null,
                child: persona.avatarUrl == null || persona.avatarUrl!.isEmpty
                    ? Icon(
                        Icons.person,
                        size: 30,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      persona.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (persona.description != null &&
                        persona.description!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        persona.description!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Selection indicator
              if (isSelected)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
